import pandas as pd
import asyncio
import aiohttp
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

@dataclass
class PredictionConfig:
    """预测配置"""
    api_key: str = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
    base_url: str = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    model: str = "gpt-4.1"
    temperature: float = 0.1
    max_concurrent: int = 10
    rate_limit_delay: float = 0.1
    retry_attempts: int = 3

async def load_prompt_template(version: str = "v2") -> str:
    """异步加载prompt模板"""
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        future = executor.submit(
            lambda: open(f"prompt/prompt_season_label_predict_{version}.txt", "r", encoding="utf-8").read()
        )
        return await loop.run_in_executor(None, lambda: future.result())

def create_prediction_payload(
    item_name: str, 
    description: str, 
    category_info: str, 
    prompt_template: str,
    config: PredictionConfig
) -> Dict:
    """创建API请求payload"""
    prompt = prompt_template.format(
        item_name=item_name,
        category_info=category_info,
        description=description
    )
    
    return {
        "model": config.model,
        "messages": [
            {
                "role": "system", 
                "content": "你是一个专业的电商商品分类专家，具有丰富的商品季节性判断经验。请仔细分析后给出明确的季节标签结论。"
            },
            {"role": "user", "content": prompt}
        ],
        "temperature": config.temperature
    }

def parse_season_label(response_text: str, item_name: str) -> str:
    """解析季节标签"""
    if not response_text:
        print(f"商品：{item_name}，空响应")
        return "未知"
    
    print(f"商品：{item_name}，模型输出: {response_text}")
    
    # 获取最后一行内容
    last_line = response_text.strip().split("\n")[-1]
    
    # 季节标签映射
    season_mapping = [
        (lambda x: "全季" in x, "全季"),
        (lambda x: "SS" in x or "春夏" in x, "SS"),
        (lambda x: "AW" in x or "秋冬" in x, "AW")
    ]
    
    for condition, label in season_mapping:
        if condition(last_line):
            return label
    
    print(f"商品：{item_name}，未识别到季节标签")
    return "未知"

async def predict_season_single(
    session: aiohttp.ClientSession,
    item_data: Tuple[str, str, str],
    prompt_template: str,
    config: PredictionConfig,
    semaphore: asyncio.Semaphore
) -> str:
    """单个商品季节预测（异步）"""
    item_name, description, category_info = item_data
    
    async with semaphore:  # 限制并发数
        payload = create_prediction_payload(
            item_name, description, category_info, prompt_template, config
        )
        
        headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
        
        for attempt in range(config.retry_attempts):
            try:
                async with session.post(
                    f"{config.base_url}/chat/completions",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        return parse_season_label(content, item_name)
                    else:
                        print(f"API错误 (尝试 {attempt + 1}): HTTP {response.status}")
                        
            except Exception as e:
                print(f"请求异常 (尝试 {attempt + 1}): {e}")
                if attempt < config.retry_attempts - 1:
                    await asyncio.sleep(config.rate_limit_delay * (attempt + 1))
        
        print(f"商品：{item_name}，预测失败，返回默认值")
        return "全季"

async def predict_seasons_batch(
    items_data: List[Tuple[str, str, str]],
    config: PredictionConfig,
    prompt_version: str = "v2"
) -> List[str]:
    """批量预测季节标签（异步并发）"""
    prompt_template = await load_prompt_template(prompt_version)
    semaphore = asyncio.Semaphore(config.max_concurrent)
    
    connector = aiohttp.TCPConnector(
        limit=config.max_concurrent,
        limit_per_host=config.max_concurrent
    )
    
    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = [
            predict_season_single(session, item_data, prompt_template, config, semaphore)
            for item_data in items_data
        ]
        
        predictions = []
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            predictions.append(result)
            
            # 显示进度
            if (i + 1) % 20 == 0:
                print(f"已完成 {i + 1}/{len(items_data)} 个预测")
            
            # 速率限制
            await asyncio.sleep(config.rate_limit_delay)
        
        return predictions

def prepare_test_data(df: pd.DataFrame, sample_size: int = 200) -> Tuple[pd.DataFrame, List[Tuple[str, str, str]]]:
    """准备测试数据"""
    print(f"数据集总量: {len(df)} 个商品")
    print("季节标签分布:")
    
    season_dist = df['季节标签'].value_counts()
    for label, count in season_dist.items():
        print(f"  {label}: {count} 个 ({count/len(df)*100:.1f}%)")
    
    # 分层采样
    sampling_func = lambda season_data, n_samples: season_data.sample(
        n=min(n_samples, len(season_data)), 
        random_state=42
    )
    
    season_samples = []
    for season in ['全季', 'SS', 'AW']:
        season_data = df[df['季节标签'] == season]
        if len(season_data) > 0:
            n_samples = max(10, int(sample_size * len(season_data) / len(df)))
            samples = sampling_func(season_data, n_samples)
            season_samples.append(samples)
    
    test_df = pd.concat(season_samples, ignore_index=True)
    
    # 准备预测输入数据
    items_data = []
    for _, row in test_df.iterrows():
        category_info = f"{row['物理一级类目']} > {row['物理二级类目']}"
        if pd.notna(row.get("物理三级类目")):
            category_info += f" > {row['物理三级类目']}"
        
        description = str(row["商品描述"]) if pd.notna(row.get("商品描述")) else ""
        
        items_data.append((
            str(row["商品名"]),
            description,
            category_info
        ))
    
    print(f"\n测试样本: {len(test_df)} 个")
    return test_df, items_data

def calculate_metrics(true_labels: List[str], predictions: List[str]) -> Dict:
    """计算评估指标"""
    correct = sum(1 for t, p in zip(true_labels, predictions) if t == p)
    accuracy = correct / len(true_labels)
    
    # 按类别计算准确率
    category_metrics = {}
    for season in ['全季', 'SS', 'AW']:
        season_indices = [i for i, t in enumerate(true_labels) if t == season]
        if season_indices:
            season_correct = sum(
                1 for i in season_indices 
                if true_labels[i] == predictions[i]
            )
            category_metrics[season] = {
                'accuracy': season_correct / len(season_indices),
                'correct': season_correct,
                'total': len(season_indices)
            }
    
    # 错误类型统计
    error_types = {}
    for t, p in zip(true_labels, predictions):
        if t != p:
            key = f"{t} → {p}"
            error_types[key] = error_types.get(key, 0) + 1
    
    return {
        'overall_accuracy': accuracy,
        'correct_count': correct,
        'total_count': len(true_labels),
        'category_metrics': category_metrics,
        'error_types': error_types
    }

def save_results(test_df: pd.DataFrame, predictions: List[str], true_labels: List[str], prompt_version: str = "v2") -> str:
    """保存预测结果"""
    results_df = test_df.copy()
    results_df['AI预测标签'] = predictions
    results_df['预测正确'] = [t == p for t, p in zip(true_labels, predictions)]

    output_file = f"assets/final_season_prediction_results_{prompt_version}_{int(time.time())}.xlsx"
    results_df.to_excel(output_file, index=False)
    return output_file

def display_cases(results_df: pd.DataFrame, case_type: str = "correct") -> None:
    """显示典型案例"""
    if case_type == "correct":
        cases = results_df[results_df['预测正确'] == True]
        title = "正确预测案例"
    else:
        cases = results_df[results_df['预测正确'] == False]
        title = "错误预测案例"
    
    if len(cases) > 0:
        print(f"\n{title}:")
        sample_cases = cases.sample(n=min(3, len(cases)))
        for i, (_, row) in enumerate(sample_cases.iterrows()):
            print(f"  案例{i+1}: {row['商品名']} | 真实:{row['季节标签']} | 预测:{row['AI预测标签']}")

async def run_final_test_async(prompt_version: str = "v2"):
    """运行最终测试（异步版本）"""
    print(f"=== 商品季节标签AI判别最终测试（提示词版本：{prompt_version}）===\n")

    # 配置
    config = PredictionConfig(max_concurrent=20, rate_limit_delay=0.05)

    # 加载并准备数据
    df = pd.read_excel("assets/上架商品季节标签.xlsx")
    test_df, items_data = prepare_test_data(df, sample_size=200)

    # 并发预测
    print(f"\n开始AI并发预测（最大并发数：{config.max_concurrent}）...")
    start_time = time.time()

    predictions = await predict_seasons_batch(items_data, config, prompt_version)

    end_time = time.time()
    print(f"预测完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 计算和显示结果
    true_labels = test_df['季节标签'].tolist()
    metrics = calculate_metrics(true_labels, predictions)
    
    print(f"\n=== 最终测试结果 ===")
    print(f"总体准确率: {metrics['overall_accuracy']:.4f} ({metrics['overall_accuracy']*100:.2f}%)")
    print(f"正确预测: {metrics['correct_count']}/{metrics['total_count']}")
    
    print(f"\n各类别准确率:")
    for season, stats in metrics['category_metrics'].items():
        print(f"  {season}: {stats['accuracy']:.4f} ({stats['accuracy']*100:.2f}%) - {stats['correct']}/{stats['total']}")
    
    print(f"\n错误类型分析:")
    for error_type, count in sorted(metrics['error_types'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {error_type}: {count} 个")
    
    # 保存结果
    output_file = save_results(test_df, predictions, true_labels, prompt_version)
    print(f"\n详细结果已保存到: {output_file}")
    
    # 显示案例
    results_df = test_df.copy()
    results_df['AI预测标签'] = predictions
    results_df['预测正确'] = [t == p for t, p in zip(true_labels, predictions)]
    
    print(f"\n=== 典型案例展示 ===")
    display_cases(results_df, "correct")
    display_cases(results_df, "error")

def run_final_test(prompt_version: str = "v2"):
    """同步入口函数"""
    asyncio.run(run_final_test_async(prompt_version))

if __name__ == "__main__":
    run_final_test()
