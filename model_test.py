from openai import OpenAI
import pandas as pd
import time
import json
import re
from typing import Optional, Dict, List
from datetime import datetime

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

def load_prompt_template():
    """加载prompt模板"""
    try:
        with open('prompt/prompt_season_label_predict_r_v1.txt', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        # 如果文件不存在，使用默认模板
        return """请根据以下商品信息判断该商品的季节标签。

## 商品信息
商品名称: {item_name}
商品类目: {category_info}
商品描述：{description}

## 季节标签定义
- 全季：适合全年使用的商品
- SS：主要适合春夏季节的商品
- AW：主要适合秋冬季节的商品

## 特殊情况
1. 包含"反季"等词汇，被认为是全季品

## 输出
请进行分析并在最后一行明确输出：
最终结论：[全季/SS/AW]"""

def predict_season_label(item_name: str, category_info: str, description: str, prompt_template: str) -> Dict:
    """使用大模型预测商品季节标签"""
    try:
        # 格式化prompt
        prompt = prompt_template.format(
            item_name=item_name or "未提供",
            category_info=category_info or "未提供",
            description=description or "未提供"
        )

        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "你是一个专业的电商商品分类专家，具有丰富的商品季节性判断经验。请仔细分析商品信息，特别注意商品的实际销售模式和市场定位。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.2,
        )

        response_text = response.choices[0].message.content

        # 提取最终结论
        predicted_label = extract_final_conclusion(response_text)

        return {
            "predicted_label": predicted_label,
            "response_text": response_text,
            "success": True,
            "error": None
        }

    except Exception as e:
        return {
            "predicted_label": None,
            "response_text": None,
            "success": False,
            "error": str(e)
        }

def extract_final_conclusion(response_text: str) -> str:
    """从模型响应中提取最终结论"""
    # 查找"最终结论："后的内容
    patterns = [
        r'最终结论：\s*([全季SS|AW]+)',
        r'最终结论:\s*([全季SS|AW]+)',
        r'结论：\s*([全季SS|AW]+)',
        r'结论:\s*([全季SS|AW]+)',
    ]

    for pattern in patterns:
        match = re.search(pattern, response_text)
        if match:
            conclusion = match.group(1).strip()
            # 标准化标签
            if conclusion in ['全季']:
                return '全季'
            elif conclusion in ['SS']:
                return 'SS'
            elif conclusion in ['AW']:
                return 'AW'

    # 如果没有找到标准格式，尝试在最后几行查找
    lines = response_text.strip().split('\n')
    for line in reversed(lines[-3:]):  # 检查最后3行
        line = line.strip()
        if any(label in line for label in ['全季', 'SS', 'AW']):
            if '全季' in line:
                return '全季'
            elif 'SS' in line:
                return 'SS'
            elif 'AW' in line:
                return 'AW'

    return "未识别"

def load_product_data(file_path: str) -> pd.DataFrame:
    """加载商品数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功加载数据，共 {len(df)} 条记录")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def validate_season_labels(df: pd.DataFrame, prompt_template: str) -> List[Dict]:
    """批量校验季节标签"""
    results = []
    total = len(df)

    print(f"开始校验 {total} 个商品的季节标签...")

    for idx, row in df.iterrows():
        print(f"处理第 {idx + 1}/{total} 个商品...")

        # 提取商品信息
        item_name = str(row.get('商品名', '')) if pd.notna(row.get('商品名')) else ''

        # 组合类目信息
        categories = []
        for col in ['物理一级类目', '物理二级类目', '物理三级类目', '物理四级类目']:
            if pd.notna(row.get(col)) and str(row.get(col)) != 'nan':
                categories.append(str(row.get(col)))
        category_info = ' > '.join(categories) if categories else ''

        description = str(row.get('商品描述', '')) if pd.notna(row.get('商品描述')) else ''
        manual_label = str(row.get('季节标签', '')) if pd.notna(row.get('季节标签')) else ''

        # 使用大模型预测
        prediction_result = predict_season_label(item_name, category_info, description, prompt_template)

        # 判断是否一致
        predicted_label = prediction_result['predicted_label']
        is_consistent = (predicted_label == manual_label) if predicted_label != "未识别" else False

        # 判断是否存在争议
        has_dispute = not is_consistent and predicted_label != "未识别"

        result = {
            'index': idx + 1,
            'item_name': item_name,
            'category_info': category_info,
            'description': description,
            'manual_label': manual_label,
            'predicted_label': predicted_label,
            'is_consistent': is_consistent,
            'has_dispute': has_dispute,
            'model_response': prediction_result['response_text'],
            'success': prediction_result['success'],
            'error': prediction_result['error']
        }

        results.append(result)

        # 添加延迟避免API限制
        time.sleep(1)

        # 每10个商品输出一次进度
        if (idx + 1) % 10 == 0:
            consistent_count = sum(1 for r in results if r['is_consistent'])
            dispute_count = sum(1 for r in results if r['has_dispute'])
            error_count = sum(1 for r in results if not r['success'])
            print(f"已处理 {idx + 1}/{total} 个商品，一致: {consistent_count}, 争议: {dispute_count}, 失败: {error_count}")

        # 如果有错误，显示错误信息
        if not prediction_result['success']:
            print(f"  ⚠️  处理失败: {prediction_result['error']}")
        elif has_dispute:
            print(f"  ⚠️  发现争议: {item_name[:30]}... 人工:{manual_label} vs 模型:{predicted_label}")

    return results

def generate_report(results: List[Dict], output_file: str):
    """生成校验报告"""
    # 统计信息
    total_count = len(results)
    consistent_count = sum(1 for r in results if r['is_consistent'])
    dispute_count = sum(1 for r in results if r['has_dispute'])
    error_count = sum(1 for r in results if not r['success'])
    unrecognized_count = sum(1 for r in results if r['predicted_label'] == "未识别")

    # 创建详细结果DataFrame
    detailed_results = []
    for r in results:
        detailed_results.append({
            '序号': r['index'],
            '商品名': r['item_name'],
            '商品类目': r['category_info'],
            '商品描述': r['description'][:100] + '...' if len(r['description']) > 100 else r['description'],
            '人工标注': r['manual_label'],
            '模型预测': r['predicted_label'],
            '是否一致': '是' if r['is_consistent'] else '否',
            '是否争议': '是' if r['has_dispute'] else '否',
            '处理状态': '成功' if r['success'] else '失败',
            '错误信息': r['error'] if r['error'] else '',
            '模型完整回复': r['model_response'] if r['model_response'] else ''
        })

    df_results = pd.DataFrame(detailed_results)

    # 保存到Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入汇总统计
        summary_data = {
            '统计项': ['总商品数', '标签一致数', '存在争议数', '处理失败数', '未识别数', '一致率', '争议率'],
            '数值': [
                total_count,
                consistent_count,
                dispute_count,
                error_count,
                unrecognized_count,
                f"{consistent_count/total_count*100:.2f}%" if total_count > 0 else "0%",
                f"{dispute_count/total_count*100:.2f}%" if total_count > 0 else "0%"
            ]
        }
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='汇总统计', index=False)

        # 写入详细结果
        df_results.to_excel(writer, sheet_name='详细结果', index=False)

        # 写入争议商品
        dispute_results = df_results[df_results['是否争议'] == '是']
        if not dispute_results.empty:
            dispute_results.to_excel(writer, sheet_name='争议商品', index=False)

    print(f"\n=== 校验完成 ===")
    print(f"总商品数: {total_count}")
    print(f"标签一致: {consistent_count} ({consistent_count/total_count*100:.2f}%)")
    print(f"存在争议: {dispute_count} ({dispute_count/total_count*100:.2f}%)")
    print(f"处理失败: {error_count}")
    print(f"未识别: {unrecognized_count}")
    print(f"结果已保存到: {output_file}")

def main():
    """主函数"""
    # 文件路径
    input_file = "assets/上架商品季节标签-check.xlsx"
    output_file = f"季节标签校验结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

    # 加载数据
    df = load_product_data(input_file)
    if df is None:
        return

    # 询问用户是否处理所有数据
    print(f"原始数据共 {len(df)} 条记录")
    choice = input("请选择处理方式：\n1. 处理所有数据\n2. 处理前100条（测试）\n3. 处理前10条（快速测试）\n请输入选择 (1/2/3): ").strip()

    if choice == "1":
        df_to_process = df
        print("将处理所有数据...")
    elif choice == "2":
        df_to_process = df.head(100)
        print("将处理前100条数据...")
    else:
        df_to_process = df.head(10)
        print("将处理前10条数据...")

    # 加载prompt模板
    prompt_template = load_prompt_template()

    # 执行校验
    results = validate_season_labels(df_to_process, prompt_template)

    # 生成报告
    generate_report(results, output_file)

if __name__ == "__main__":
    main()