from openai import OpenAI
import time
from typing import Optional, Dict

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "你是一个专业的电商商品分类专家，具有丰富的商品季节性判断经验。请仔细分析商品信息，特别注意商品的实际销售模式和市场定位。"},
                {"role": "user", "content": "你好"},
            ],
            temperature=0.2,
        )
print(response)