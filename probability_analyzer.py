# -*- coding: utf-8 -*-
"""
概率结果分析工具
用于分析不同阈值下的匹配效果
"""

import pandas as pd
import numpy as np
from typing import List, Dict

def analyze_probability_results(excel_file: str, custom_thresholds: List[float] = None):
    """分析概率结果，支持自定义阈值"""
    
    # 读取详细结果
    df = pd.read_excel(excel_file, sheet_name='详细结果')
    
    # 解析概率分布
    def parse_probabilities(prob_str):
        """解析概率字符串"""
        try:
            parts = prob_str.split()
            probs = {}
            for part in parts:
                if ':' in part:
                    label, prob = part.split(':')
                    probs[label] = float(prob.replace('%', ''))
            return probs
        except:
            return {'全季': 0, 'SS': 0, 'AW': 0}
    
    # 解析所有商品的概率
    df['概率字典'] = df['概率分布'].apply(parse_probabilities)
    
    # 提取人工标注概率
    df['人工标注概率值'] = df.apply(lambda row: row['概率字典'].get(row['人工标注'], 0), axis=1)
    
    # 如果没有提供自定义阈值，使用默认阈值
    if custom_thresholds is None:
        custom_thresholds = [10, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80]
    
    # 计算不同阈值下的匹配率
    threshold_results = []
    for threshold in custom_thresholds:
        matches = sum(1 for prob in df['人工标注概率值'] if prob >= threshold)
        total = len(df)
        accuracy = matches / total * 100 if total > 0 else 0
        
        threshold_results.append({
            '阈值': f"{threshold}%",
            '匹配数量': matches,
            '总数量': total,
            '准确率': f"{accuracy:.2f}%",
            '准确率数值': accuracy
        })
    
    # 创建阈值分析DataFrame
    threshold_df = pd.DataFrame(threshold_results)
    
    # 分析概率分布
    prob_distribution = {
        '0-10%': sum(1 for p in df['人工标注概率值'] if 0 <= p < 10),
        '10-20%': sum(1 for p in df['人工标注概率值'] if 10 <= p < 20),
        '20-30%': sum(1 for p in df['人工标注概率值'] if 20 <= p < 30),
        '30-40%': sum(1 for p in df['人工标注概率值'] if 30 <= p < 40),
        '40-50%': sum(1 for p in df['人工标注概率值'] if 40 <= p < 50),
        '50-60%': sum(1 for p in df['人工标注概率值'] if 50 <= p < 60),
        '60-70%': sum(1 for p in df['人工标注概率值'] if 60 <= p < 70),
        '70-80%': sum(1 for p in df['人工标注概率值'] if 70 <= p < 80),
        '80-90%': sum(1 for p in df['人工标注概率值'] if 80 <= p < 90),
        '90-100%': sum(1 for p in df['人工标注概率值'] if 90 <= p <= 100)
    }
    
    # 找出最佳阈值
    best_threshold_idx = threshold_df['准确率数值'].idxmax()
    best_threshold = threshold_df.iloc[best_threshold_idx]
    
    # 输出分析结果
    print("=== 概率结果深度分析 ===")
    print(f"总商品数: {len(df)}")
    print(f"平均人工标注概率: {df['人工标注概率值'].mean():.2f}%")
    print(f"人工标注概率中位数: {df['人工标注概率值'].median():.2f}%")
    print(f"人工标注概率标准差: {df['人工标注概率值'].std():.2f}%")
    
    print(f"\n=== 最佳阈值推荐 ===")
    print(f"推荐阈值: {best_threshold['阈值']}")
    print(f"该阈值下准确率: {best_threshold['准确率']}")
    print(f"匹配商品数: {best_threshold['匹配数量']}/{best_threshold['总数量']}")
    
    print(f"\n=== 概率分布统计 ===")
    for range_name, count in prob_distribution.items():
        percentage = count / len(df) * 100 if len(df) > 0 else 0
        print(f"{range_name}: {count}个商品 ({percentage:.1f}%)")
    
    print(f"\n=== 不同阈值下的准确率 ===")
    print(threshold_df.to_string(index=False))
    
    # 分析低概率商品（可能的标注错误）
    low_prob_items = df[df['人工标注概率值'] < 20].copy()
    if not low_prob_items.empty:
        print(f"\n=== 低概率商品分析（<20%，可能存在标注问题）===")
        print(f"共 {len(low_prob_items)} 个商品")
        for _, row in low_prob_items.iterrows():
            print(f"序号{row['序号']}: {row['商品名'][:30]}...")
            print(f"  人工标注: {row['人工标注']} ({row['人工标注概率值']:.1f}%)")
            print(f"  AI推荐: {row['推荐标签']}")
            print(f"  概率分布: {row['概率分布']}")
            print()
    
    # 分析高争议商品（概率接近的情况）
    high_dispute_items = []
    for _, row in df.iterrows():
        probs = row['概率字典']
        sorted_probs = sorted(probs.values(), reverse=True)
        if len(sorted_probs) >= 2 and sorted_probs[0] - sorted_probs[1] < 20:  # 最高和第二高概率差距小于20%
            high_dispute_items.append(row)
    
    if high_dispute_items:
        print(f"\n=== 高争议商品分析（概率接近，差距<20%）===")
        print(f"共 {len(high_dispute_items)} 个商品")
        for row in high_dispute_items[:5]:  # 只显示前5个
            print(f"序号{row['序号']}: {row['商品名'][:30]}...")
            print(f"  人工标注: {row['人工标注']}")
            print(f"  概率分布: {row['概率分布']}")
            print()
    
    return threshold_df, prob_distribution, low_prob_items

def recommend_optimal_threshold(excel_file: str):
    """推荐最优阈值"""
    threshold_df, _, _ = analyze_probability_results(excel_file)
    
    # 找到准确率最高的阈值
    best_idx = threshold_df['准确率数值'].idxmax()
    best_threshold = threshold_df.iloc[best_idx]
    
    # 找到准确率达到90%以上的最低阈值
    high_accuracy_thresholds = threshold_df[threshold_df['准确率数值'] >= 90]
    if not high_accuracy_thresholds.empty:
        min_threshold_90 = high_accuracy_thresholds.iloc[-1]  # 最后一个（最低阈值）
        
        print(f"\n=== 阈值推荐 ===")
        print(f"最高准确率阈值: {best_threshold['阈值']} (准确率: {best_threshold['准确率']})")
        print(f"达到90%准确率的最低阈值: {min_threshold_90['阈值']} (准确率: {min_threshold_90['准确率']})")
        
        return {
            'best_threshold': best_threshold['阈值'],
            'best_accuracy': best_threshold['准确率'],
            'threshold_90': min_threshold_90['阈值'],
            'accuracy_90': min_threshold_90['准确率']
        }
    else:
        print(f"\n=== 阈值推荐 ===")
        print(f"最高准确率阈值: {best_threshold['阈值']} (准确率: {best_threshold['准确率']})")
        print("注意：没有阈值能达到90%以上的准确率")
        
        return {
            'best_threshold': best_threshold['阈值'],
            'best_accuracy': best_threshold['准确率'],
            'threshold_90': None,
            'accuracy_90': None
        }

def main():
    """主函数"""
    print("=== 概率结果分析工具 ===")
    
    # 查找最新的概率结果文件
    import glob
    import os
    
    prob_files = glob.glob("assets/季节标签校验结果_概率版_*.xlsx")
    if not prob_files:
        print("未找到概率版本的结果文件")
        return
    
    # 使用最新的文件
    latest_file = max(prob_files, key=os.path.getctime)
    print(f"分析文件: {latest_file}")
    
    # 询问是否使用自定义阈值
    use_custom = input("是否使用自定义阈值？(y/n，默认n): ").strip().lower()
    
    if use_custom == 'y':
        threshold_input = input("请输入阈值列表，用逗号分隔（如：10,20,30,40,50）: ").strip()
        try:
            custom_thresholds = [float(x.strip()) for x in threshold_input.split(',')]
            print(f"使用自定义阈值: {custom_thresholds}")
        except:
            print("输入格式错误，使用默认阈值")
            custom_thresholds = None
    else:
        custom_thresholds = None
    
    # 执行分析
    analyze_probability_results(latest_file, custom_thresholds)
    
    # 推荐最优阈值
    recommend_optimal_threshold(latest_file)

if __name__ == "__main__":
    main()
