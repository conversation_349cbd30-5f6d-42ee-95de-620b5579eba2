# -*- coding: utf-8 -*-
"""
商品季节标签校验工具 - 概率版本
支持概率输出和多种匹配规则
"""

from openai import OpenAI
import pandas as pd
import time
import json
import re
from typing import Optional, Dict, List, Tuple
from datetime import datetime
import concurrent.futures
import threading
from tqdm import tqdm

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

# 线程安全的统计计数器
class ThreadSafeCounter:
    def __init__(self):
        self._lock = threading.Lock()
        self.processed = 0
        self.strict_match = 0
        self.threshold_30_match = 0
        self.threshold_40_match = 0
        self.threshold_50_match = 0
        self.top2_match = 0
        self.error = 0
    
    def update(self, matches: Dict[str, bool], has_error: bool = False):
        with self._lock:
            self.processed += 1
            if has_error:
                self.error += 1
                return
            
            if matches.get('strict', False):
                self.strict_match += 1
            if matches.get('threshold_30', False):
                self.threshold_30_match += 1
            if matches.get('threshold_40', False):
                self.threshold_40_match += 1
            if matches.get('threshold_50', False):
                self.threshold_50_match += 1
            if matches.get('top2', False):
                self.top2_match += 1
    
    def get_stats(self):
        with self._lock:
            return {
                'processed': self.processed,
                'strict_match': self.strict_match,
                'threshold_30_match': self.threshold_30_match,
                'threshold_40_match': self.threshold_40_match,
                'threshold_50_match': self.threshold_50_match,
                'top2_match': self.top2_match,
                'error': self.error
            }

def load_prompt_template(use_probability: bool = True):
    """加载prompt模板"""
    if use_probability:
        template_file = 'prompt/prompt_season_label_probability_v1.txt'
    else:
        template_file = 'prompt/prompt_season_label_predict_r_v1.txt'
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        # 如果文件不存在，使用默认模板
        if use_probability:
            return """请根据以下商品信息判断该商品的季节标签，并给出每个标签的概率。

## 商品信息
商品名称: {item_name}
商品类目: {category_info}
商品描述：{description}

## 季节标签定义
- 全季：适合全年使用的商品
- SS：主要适合春夏季节的商品
- AW：主要适合秋冬季节的商品

## 输出要求
请按照以下格式输出：

### 概率分析
全季: X%
SS: Y%
AW: Z%

### 最终结论
推荐标签: [全季/SS/AW]"""
        else:
            return """请根据以下商品信息判断该商品的季节标签。

商品名称: {item_name}
商品类目: {category_info}
商品描述：{description}

季节标签定义：
- 全季：适合全年使用的商品
- SS：主要适合春夏季节的商品
- AW：主要适合秋冬季节的商品

请在最后一行明确输出：
最终结论：[全季/SS/AW]"""

def extract_probabilities(response_text: str) -> Tuple[Dict[str, float], str]:
    """从模型响应中提取概率信息和推荐标签"""
    probabilities = {'全季': 0.0, 'SS': 0.0, 'AW': 0.0}
    recommended_label = "未识别"
    
    # 提取概率
    prob_patterns = [
        r'全季[：:]\s*(\d+(?:\.\d+)?)%',
        r'SS[：:]\s*(\d+(?:\.\d+)?)%', 
        r'AW[：:]\s*(\d+(?:\.\d+)?)%'
    ]
    
    labels = ['全季', 'SS', 'AW']
    for i, pattern in enumerate(prob_patterns):
        match = re.search(pattern, response_text)
        if match:
            probabilities[labels[i]] = float(match.group(1))
    
    # 归一化概率（如果总和不是100%）
    total_prob = sum(probabilities.values())
    if total_prob > 0 and abs(total_prob - 100) > 1:  # 允许1%的误差
        for label in probabilities:
            probabilities[label] = probabilities[label] / total_prob * 100
    
    # 提取推荐标签
    recommend_patterns = [
        r'推荐标签[：:]\s*([全季SS|AW]+)',
        r'最终结论[：:]\s*([全季SS|AW]+)',
        r'结论[：:]\s*([全季SS|AW]+)'
    ]
    
    for pattern in recommend_patterns:
        match = re.search(pattern, response_text)
        if match:
            label = match.group(1).strip()
            if label in ['全季', 'SS', 'AW']:
                recommended_label = label
                break
    
    # 如果没有找到推荐标签，使用概率最高的标签
    if recommended_label == "未识别" and max(probabilities.values()) > 0:
        recommended_label = max(probabilities, key=probabilities.get)
    
    return probabilities, recommended_label

def predict_season_label_with_probability(item_name: str, category_info: str, description: str, prompt_template: str) -> Dict:
    """使用大模型预测商品季节标签（概率版本）"""
    try:
        # 格式化prompt
        prompt = prompt_template.format(
            item_name=item_name or "未提供",
            category_info=category_info or "未提供", 
            description=description or "未提供"
        )
        
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "你是一个专业的电商商品分类专家，具有丰富的商品季节性判断经验。请仔细分析商品信息，给出每个季节标签的概率，并说明你的推理过程。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.2,
        )
        
        response_text = response.choices[0].message.content
        
        # 提取概率和推荐标签
        probabilities, recommended_label = extract_probabilities(response_text)
        
        return {
            "probabilities": probabilities,
            "recommended_label": recommended_label,
            "response_text": response_text,
            "success": True,
            "error": None
        }
        
    except Exception as e:
        return {
            "probabilities": {'全季': 0.0, 'SS': 0.0, 'AW': 0.0},
            "recommended_label": None,
            "response_text": None,
            "success": False,
            "error": str(e)
        }

def evaluate_matches(probabilities: Dict[str, float], manual_label: str, recommended_label: str) -> Dict[str, bool]:
    """评估不同匹配规则下的结果"""
    matches = {}
    
    # 严格匹配：推荐标签与人工标注一致
    matches['strict'] = (recommended_label == manual_label)
    
    # 阈值匹配：人工标注标签的概率超过阈值
    manual_prob = probabilities.get(manual_label, 0)
    matches['threshold_30'] = (manual_prob >= 30)
    matches['threshold_40'] = (manual_prob >= 40) 
    matches['threshold_50'] = (manual_prob >= 50)
    
    # Top2匹配：人工标注在概率前2名中
    sorted_labels = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)
    top2_labels = [label for label, prob in sorted_labels[:2]]
    matches['top2'] = (manual_label in top2_labels)
    
    return matches

def load_product_data(file_path: str) -> pd.DataFrame:
    """加载商品数据"""
    try:
        df = pd.read_excel(file_path)
        print(f"成功加载数据，共 {len(df)} 条记录")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def process_single_item_probability(args):
    """处理单个商品的函数（概率版本）"""
    idx, row, prompt_template, counter, pbar = args

    try:
        # 提取商品信息
        item_name = str(row.get('商品名', '')) if pd.notna(row.get('商品名')) else ''

        # 组合类目信息
        categories = []
        for col in ['物理一级类目', '物理二级类目', '物理三级类目', '物理四级类目']:
            if pd.notna(row.get(col)) and str(row.get(col)) != 'nan':
                categories.append(str(row.get(col)))
        category_info = ' > '.join(categories) if categories else ''

        description = str(row.get('商品描述', '')) if pd.notna(row.get('商品描述')) else ''
        manual_label = str(row.get('季节标签', '')) if pd.notna(row.get('季节标签')) else ''

        # 使用大模型预测
        prediction_result = predict_season_label_with_probability(item_name, category_info, description, prompt_template)

        if prediction_result['success']:
            # 评估不同匹配规则
            matches = evaluate_matches(
                prediction_result['probabilities'],
                manual_label,
                prediction_result['recommended_label']
            )

            # 更新统计计数器
            counter.update(matches=matches, has_error=False)
        else:
            matches = {key: False for key in ['strict', 'threshold_30', 'threshold_40', 'threshold_50', 'top2']}
            counter.update(matches=matches, has_error=True)

        # 更新进度条
        stats = counter.get_stats()
        pbar.set_postfix({
            '严格': f"{stats['strict_match']}/{stats['processed']}",
            '30%': f"{stats['threshold_30_match']}/{stats['processed']}",
            '失败': stats['error']
        })
        pbar.update(1)

        result = {
            'index': idx + 1,
            'item_name': item_name,
            'category_info': category_info,
            'description': description,
            'manual_label': manual_label,
            'probabilities': prediction_result['probabilities'],
            'recommended_label': prediction_result['recommended_label'],
            'matches': matches,
            'model_response': prediction_result['response_text'],
            'success': prediction_result['success'],
            'error': prediction_result['error']
        }

        return result

    except Exception as e:
        matches = {key: False for key in ['strict', 'threshold_30', 'threshold_40', 'threshold_50', 'top2']}
        counter.update(matches=matches, has_error=True)
        pbar.update(1)
        return {
            'index': idx + 1,
            'item_name': '',
            'category_info': '',
            'description': '',
            'manual_label': '',
            'probabilities': {'全季': 0.0, 'SS': 0.0, 'AW': 0.0},
            'recommended_label': '',
            'matches': matches,
            'model_response': '',
            'success': False,
            'error': str(e)
        }

def validate_season_labels_probability(df: pd.DataFrame, prompt_template: str, max_workers: int = 5) -> List[Dict]:
    """并发批量校验季节标签（概率版本）"""
    total = len(df)
    print(f"开始并发校验 {total} 个商品的季节标签（概率模式），使用 {max_workers} 个线程...")

    # 创建线程安全的计数器
    counter = ThreadSafeCounter()

    # 创建进度条
    with tqdm(total=total, desc="处理商品") as pbar:
        # 准备参数
        args_list = [(idx, row, prompt_template, counter, pbar) for idx, row in df.iterrows()]

        # 使用线程池并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(executor.map(process_single_item_probability, args_list))

    # 按索引排序结果
    results.sort(key=lambda x: x['index'])

    return results

def generate_probability_report(results: List[Dict], output_file: str):
    """生成概率版本的校验报告"""
    total_count = len(results)
    successful_results = [r for r in results if r['success']]
    error_count = total_count - len(successful_results)

    # 统计不同匹配规则的准确率
    match_stats = {
        'strict': sum(1 for r in successful_results if r['matches']['strict']),
        'threshold_30': sum(1 for r in successful_results if r['matches']['threshold_30']),
        'threshold_40': sum(1 for r in successful_results if r['matches']['threshold_40']),
        'threshold_50': sum(1 for r in successful_results if r['matches']['threshold_50']),
        'top2': sum(1 for r in successful_results if r['matches']['top2'])
    }

    # 创建详细结果DataFrame
    detailed_results = []
    for r in results:
        prob_str = f"全季:{r['probabilities']['全季']:.1f}% SS:{r['probabilities']['SS']:.1f}% AW:{r['probabilities']['AW']:.1f}%"

        detailed_results.append({
            '序号': r['index'],
            '商品名': r['item_name'],
            '商品类目': r['category_info'],
            '商品描述': r['description'][:100] + '...' if len(r['description']) > 100 else r['description'],
            '人工标注': r['manual_label'],
            '推荐标签': r['recommended_label'],
            '概率分布': prob_str,
            '人工标注概率': f"{r['probabilities'].get(r['manual_label'], 0):.1f}%",
            '严格匹配': '是' if r['matches']['strict'] else '否',
            '30%阈值匹配': '是' if r['matches']['threshold_30'] else '否',
            '40%阈值匹配': '是' if r['matches']['threshold_40'] else '否',
            '50%阈值匹配': '是' if r['matches']['threshold_50'] else '否',
            'Top2匹配': '是' if r['matches']['top2'] else '否',
            '处理状态': '成功' if r['success'] else '失败',
            '错误信息': r['error'] if r['error'] else '',
            '模型完整回复': r['model_response'] if r['model_response'] else ''
        })

    df_results = pd.DataFrame(detailed_results)

    # 保存到Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入汇总统计
        summary_data = {
            '匹配规则': [
                '总商品数',
                '处理成功数',
                '处理失败数',
                '严格匹配（推荐=人工）',
                '30%阈值匹配',
                '40%阈值匹配',
                '50%阈值匹配',
                'Top2匹配'
            ],
            '数量': [
                total_count,
                len(successful_results),
                error_count,
                match_stats['strict'],
                match_stats['threshold_30'],
                match_stats['threshold_40'],
                match_stats['threshold_50'],
                match_stats['top2']
            ],
            '准确率': [
                '100%',
                f"{len(successful_results)/total_count*100:.2f}%" if total_count > 0 else "0%",
                f"{error_count/total_count*100:.2f}%" if total_count > 0 else "0%",
                f"{match_stats['strict']/len(successful_results)*100:.2f}%" if successful_results else "0%",
                f"{match_stats['threshold_30']/len(successful_results)*100:.2f}%" if successful_results else "0%",
                f"{match_stats['threshold_40']/len(successful_results)*100:.2f}%" if successful_results else "0%",
                f"{match_stats['threshold_50']/len(successful_results)*100:.2f}%" if successful_results else "0%",
                f"{match_stats['top2']/len(successful_results)*100:.2f}%" if successful_results else "0%"
            ]
        }
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='匹配规则统计', index=False)

        # 写入详细结果
        df_results.to_excel(writer, sheet_name='详细结果', index=False)

        # 写入严格匹配失败的商品
        strict_mismatch = df_results[df_results['严格匹配'] == '否']
        if not strict_mismatch.empty:
            strict_mismatch.to_excel(writer, sheet_name='严格匹配失败', index=False)

        # 写入概率分析
        prob_analysis = []
        for r in successful_results:
            manual_prob = r['probabilities'].get(r['manual_label'], 0)
            max_prob = max(r['probabilities'].values())
            max_label = max(r['probabilities'], key=r['probabilities'].get)

            prob_analysis.append({
                '序号': r['index'],
                '商品名': r['item_name'][:50] + '...' if len(r['item_name']) > 50 else r['item_name'],
                '人工标注': r['manual_label'],
                '人工标注概率': f"{manual_prob:.1f}%",
                '最高概率标签': max_label,
                '最高概率': f"{max_prob:.1f}%",
                '概率差距': f"{max_prob - manual_prob:.1f}%",
                '全季概率': f"{r['probabilities']['全季']:.1f}%",
                'SS概率': f"{r['probabilities']['SS']:.1f}%",
                'AW概率': f"{r['probabilities']['AW']:.1f}%"
            })

        df_prob_analysis = pd.DataFrame(prob_analysis)
        df_prob_analysis.to_excel(writer, sheet_name='概率分析', index=False)

    print(f"\n=== 概率版本校验完成 ===")
    print(f"总商品数: {total_count}")
    print(f"处理成功: {len(successful_results)} ({len(successful_results)/total_count*100:.2f}%)")
    print(f"处理失败: {error_count}")
    print(f"\n=== 不同匹配规则的准确率 ===")
    if successful_results:
        print(f"严格匹配: {match_stats['strict']}/{len(successful_results)} ({match_stats['strict']/len(successful_results)*100:.2f}%)")
        print(f"30%阈值匹配: {match_stats['threshold_30']}/{len(successful_results)} ({match_stats['threshold_30']/len(successful_results)*100:.2f}%)")
        print(f"40%阈值匹配: {match_stats['threshold_40']}/{len(successful_results)} ({match_stats['threshold_40']/len(successful_results)*100:.2f}%)")
        print(f"50%阈值匹配: {match_stats['threshold_50']}/{len(successful_results)} ({match_stats['threshold_50']/len(successful_results)*100:.2f}%)")
        print(f"Top2匹配: {match_stats['top2']}/{len(successful_results)} ({match_stats['top2']/len(successful_results)*100:.2f}%)")
    print(f"结果已保存到: {output_file}")

def main():
    """主函数"""
    print("=== 商品季节标签校验工具 - 概率版本 ===")
    print("本版本支持概率输出和多种匹配规则")

    # 文件路径
    input_file = "assets/上架商品季节标签-check.xlsx"
    output_file = f"assets/季节标签校验结果_概率版_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

    # 加载数据
    df = load_product_data(input_file)
    if df is None:
        return

    # 询问用户处理数量
    print(f"\n原始数据共 {len(df)} 条记录")
    choice = input("请选择处理方式：\n1. 处理所有数据\n2. 处理前100条（测试）\n3. 处理前10条（快速测试）\n4. 自定义数量\n请输入选择 (1/2/3/4): ").strip()

    if choice == "1":
        df_to_process = df
        print("将处理所有数据...")
        estimated_time = len(df) / 3.0 / 60  # 概率版本稍慢一些
        print(f"预计需要约 {estimated_time:.1f} 分钟")
    elif choice == "2":
        df_to_process = df.head(100)
        print("将处理前100条数据...")
    elif choice == "4":
        try:
            custom_count = int(input("请输入要处理的数量: ").strip())
            custom_count = max(1, min(custom_count, len(df)))
            df_to_process = df.head(custom_count)
            print(f"将处理前{custom_count}条数据...")
        except ValueError:
            df_to_process = df.head(10)
            print("输入无效，将处理前10条数据...")
    else:
        df_to_process = df.head(10)
        print("将处理前10条数据...")

    # 询问并发线程数
    try:
        max_workers = int(input("请输入并发线程数 (建议3-6，默认5): ").strip() or "5")
        max_workers = max(1, min(max_workers, 8))  # 概率版本限制更严格
    except ValueError:
        max_workers = 5

    # 加载prompt模板
    prompt_template = load_prompt_template(use_probability=True)

    # 记录开始时间
    start_time = time.time()

    # 执行校验
    print(f"\n使用概率模式，并发线程数: {max_workers}")
    results = validate_season_labels_probability(df_to_process, prompt_template, max_workers)

    # 计算处理时间
    end_time = time.time()
    processing_time = end_time - start_time

    print(f"\n处理完成，总耗时: {processing_time:.1f} 秒")
    print(f"平均处理速度: {len(df_to_process)/processing_time:.2f} 个商品/秒")

    # 生成报告
    generate_probability_report(results, output_file)

if __name__ == "__main__":
    main()
