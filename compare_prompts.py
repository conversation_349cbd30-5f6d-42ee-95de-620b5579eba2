import asyncio
import pandas as pd
import time
from final_season_test import run_final_test_async, prepare_test_data, calculate_metrics

async def compare_prompt_versions():
    """对比不同版本的提示词效果"""
    print("=== 提示词版本对比测试 ===\n")
    
    # 加载数据
    df = pd.read_excel("assets/上架商品季节标签.xlsx")
    test_df, items_data = prepare_test_data(df, sample_size=100)  # 使用较小样本进行快速对比
    true_labels = test_df['季节标签'].tolist()
    
    results = {}
    
    # 测试v1版本
    print("测试提示词 v1 版本...")
    start_time = time.time()
    await run_final_test_async("v1")
    v1_time = time.time() - start_time
    
    print("\n" + "="*50 + "\n")
    
    # 测试v2版本
    print("测试提示词 v2 版本...")
    start_time = time.time()
    await run_final_test_async("v2")
    v2_time = time.time() - start_time
    
    print(f"\n=== 对比总结 ===")
    print(f"v1版本耗时: {v1_time:.2f} 秒")
    print(f"v2版本耗时: {v2_time:.2f} 秒")

if __name__ == "__main__":
    asyncio.run(compare_prompt_versions())
