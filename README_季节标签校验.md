# 商品季节标签校验工具

本工具使用大模型校验商品的季节标签是否正确或存在争议。

## 功能特点

- ✅ 支持并发处理，大幅提升处理速度
- ✅ 自动识别争议商品
- ✅ 生成详细的Excel报告
- ✅ 实时进度显示
- ✅ 错误处理和重试机制

## 季节标签定义

- **全季**：适合全年使用的商品
- **SS**：主要适合春夏季节的商品  
- **AW**：主要适合秋冬季节的商品

## 文件说明

### 主要脚本

1. **model_test.py** - 交互式版本
   - 支持选择处理数量（10条/100条/全部/自定义）
   - 支持选择处理模式（并发/串行）
   - 适合测试和小批量处理

2. **season_label_validator_full.py** - 完整版本
   - 专门用于处理所有数据
   - 默认使用8个并发线程
   - 优化的性能配置

### 输入文件

- **assets/上架商品季节标签-check.xlsx** - 包含商品信息和人工标注的季节标签

### 输出文件

生成的Excel文件包含三个工作表：

1. **汇总统计** - 整体统计信息
2. **详细结果** - 每个商品的详细校验结果
3. **争议商品** - 存在争议的商品列表

## 使用方法

### 方法1：交互式处理

```bash
python3 model_test.py
```

按提示选择：
- 处理数量（1-4）
- 处理模式（1-2）
- 并发线程数（建议3-8）

### 方法2：处理所有数据

```bash
python3 season_label_validator_full.py
```

确认后自动处理所有数据。

## 性能参考

基于测试结果：

- **并发处理**：约3-4个商品/秒
- **串行处理**：约1个商品/秒
- **7219条数据**：预计需要30-35分钟（并发模式）

## 争议案例分析

常见的争议类型：

1. **季节性服装**
   - 羽绒服：人工标注"全季" vs 模型预测"AW"
   - 长袖衣物：人工标注"全季" vs 模型预测"AW"

2. **季节性配饰**
   - 羊毛帽子：人工标注"全季" vs 模型预测"AW"
   - 露营用品：人工标注"全季" vs 模型预测"SS"

3. **特殊情况**
   - 包含"反季"关键词的商品通常被认为是全季品

## 注意事项

1. **API限制**：请确保API密钥有效且有足够的调用额度
2. **网络稳定**：处理大量数据时需要稳定的网络连接
3. **内存使用**：处理全量数据时会占用一定内存
4. **文件权限**：确保有写入权限以保存结果文件

## 错误处理

工具包含完善的错误处理机制：

- API调用失败会记录错误信息
- 网络超时会自动重试
- 处理失败的商品会在报告中标记

## 结果解读

### 统计指标

- **一致率**：模型预测与人工标注一致的比例
- **争议率**：存在争议的商品比例
- **失败率**：处理失败的商品比例

### 争议商品

对于争议商品，建议：

1. 查看模型的完整回复了解推理过程
2. 结合商品的实际特性进行人工复核
3. 考虑商品的市场定位和销售模式

## 技术实现

- **并发处理**：使用ThreadPoolExecutor实现多线程并发
- **进度显示**：使用tqdm库显示实时进度
- **线程安全**：使用锁机制确保统计数据的准确性
- **错误恢复**：异常处理确保单个商品失败不影响整体处理

## 依赖包

```bash
pip install openai pandas tqdm openpyxl
```

## 更新日志

- v1.0：基础功能实现
- v1.1：添加并发处理支持
- v1.2：优化性能和错误处理
- v1.3：添加详细的进度显示和统计信息
